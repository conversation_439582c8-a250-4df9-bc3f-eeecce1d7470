# MTProxy Configuration with Authentication
# Format for SOCKS5 proxy: socks5 <ip>:<port> <username> <password>;
# Format for HTTP proxy: http <ip>:<port> <username> <password>;

# Example SOCKS5 proxy with auth
socks5 your-proxy-server.com:1080 your_username your_password;

# Example HTTP proxy with auth  
http your-proxy-server.com:8080 your_username your_password;

# Multiple proxy servers for redundancy
socks5 proxy1.example.com:1080 user1 pass1;
socks5 proxy2.example.com:1080 user2 pass2;

# Fallback to direct Telegram servers (optional)
proxy **************:443;
proxy **************:443;
