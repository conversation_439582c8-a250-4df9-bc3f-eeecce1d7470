# MTProxy Configuration File
# This file contains configuration for Telegram MTProxy

# Proxy configuration
proxy {
    # Listen on all interfaces
    listen = 0.0.0.0:443;
    
    # Enable statistics
    stats = 0.0.0.0:8888;
    
    # Worker processes
    workers = 4;
    
    # Log level (error, warning, notice, info, debug)
    log_level = info;
    
    # Maximum connections
    max_connections = 60000;
    
    # Buffer sizes
    buffer_size = 16384;
    
    # Timeout settings
    timeout = 10;
    
    # Enable keep-alive
    keepalive = true;
}

# Security settings
security {
    # Disable dangerous features
    disable_random_padding = false;
    
    # Enable secure mode
    secure_mode = true;
}
