# Telegram MTProxy Docker Setup

Đ<PERSON><PERSON> là setup Docker cho Telegram MTProxy - một proxy server cho Telegram.

## 🚀 C<PERSON>ch sử dụng

### 1. Build và chạy
```bash
# Build image
docker compose build

# Chạy container
docker compose up -d

# Xem logs
docker compose logs -f mtproxy

# Dừng container
docker compose down
```

### 2. <PERSON><PERSON><PERSON> hình

#### File `.env`
Chứa các biến môi trường:
- `SECRET`: Secret key 32 ký tự hex (16 bytes)
- `PROXY_TAG`: Proxy tag 32 ký tự hex (16 bytes) 
- `PORT`: Port chính của proxy (mặc định: 9443)
- `STATS_PORT`: Port thống kê (mặc định: 8888)
- `WORKERS`: Số worker processes (mặc định: 4)

#### File `proxy-multi.conf`
Chứa danh sách các Telegram servers:
```
proxy **************:443;
proxy **************:443;
proxy ***************:443;
proxy **************:443;
proxy *************:443;
```

### 3. Tạo secret mới
```bash
# Tạo secret 32 ký tự hex
head -c 16 /dev/urandom | xxd -ps
```

### 4. Kết nối Telegram

Sau khi proxy chạy thành công, sử dụng thông tin sau để kết nối:

- **Server**: IP của máy chủ
- **Port**: 9443 (hoặc PORT trong .env)
- **Secret**: Giá trị SECRET trong .env

### 5. Kiểm tra trạng thái

```bash
# Xem logs
docker compose logs mtproxy

# Kiểm tra container
docker compose ps

# Xem thống kê (nếu có)
# Stats port: 8888
```

## 📁 Cấu trúc files

```
.
├── Dockerfile              # Docker image definition
├── docker-compose.yml      # Docker compose configuration
├── .env                    # Environment variables
├── proxy-multi.conf        # MTProxy configuration
└── README.md              # This file
```

## 🔧 Troubleshooting

### Container restart liên tục
- Kiểm tra SECRET và PROXY_TAG có đúng 32 ký tự hex không
- Kiểm tra format file proxy-multi.conf

### Không kết nối được
- Kiểm tra firewall cho port 9443
- Kiểm tra logs: `docker compose logs mtproxy`

### Tạo secret mới
```bash
# Tạo secret
openssl rand -hex 16

# Hoặc
head -c 16 /dev/urandom | xxd -ps
```

## ⚠️ Lưu ý bảo mật

- Thay đổi SECRET và PROXY_TAG mặc định
- Không chia sẻ secret công khai
- Sử dụng firewall để bảo vệ stats port (8888)
