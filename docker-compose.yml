version: '3.8'

services:
  mtproxy:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mtproxy
    ports:
      - "${PORT}:${PORT}"
      - "${STATS_PORT}:${STATS_PORT}"
    volumes:
      - ./proxy-multi.conf:/opt/MTProxy/proxy-multi.conf
    # Uncomment below to use custom server config
    # - ./custom-proxy.conf:/opt/MTProxy/proxy-multi.conf
    environment:
      - SECRET=${SECRET}
      - PROXY_TAG=${PROXY_TAG}
      - PORT=${PORT}
      - STATS_PORT=${STATS_PORT}
      - WORKERS=${WORKERS}
    restart: unless-stopped
    networks:
      - mtproxy-network

networks:
  mtproxy-network:
    driver: bridge