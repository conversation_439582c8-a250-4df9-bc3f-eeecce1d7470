version: '3.8'

services:
  mtproxy-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mtproxy-backend
    ports:
      - "8338:8338"
      - "8888:8888"
    environment:
      - SECRET=1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d
      - PROXY_TAG=1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d
      - PORT=8338
      - STATS_PORT=8888
      - WORKERS=1
    restart: unless-stopped
    networks:
      - mtproxy-network

networks:
  mtproxy-network:
    driver: bridge