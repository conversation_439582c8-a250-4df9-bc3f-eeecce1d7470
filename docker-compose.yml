version: '3.8'

services:
  mtproxy-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mtproxy-backend
    ports:
      - "9443:9443"
    volumes:
      - ./proxy-multi.conf:/opt/MTProxy/proxy-multi.conf
    environment:
      - SECRET=1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d
      - PORT=9443
    restart: unless-stopped
    networks:
      - mtproxy-network

networks:
  mtproxy-network:
    driver: bridge